package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__158;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.KnowledgeBaseBoToKnowledgeBaseMapper__1;
import org.dromara.app.domain.vo.KnowledgeBaseVo;
import org.dromara.app.domain.vo.KnowledgeBaseVoToKnowledgeBaseMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__158.class,
    uses = {KnowledgeBaseBoToKnowledgeBaseMapper__1.class,KnowledgeBaseVoToKnowledgeBaseMapper__1.class},
    imports = {}
)
public interface KnowledgeBaseToKnowledgeBaseVoMapper__1 extends BaseMapper<KnowledgeBase, KnowledgeBaseVo> {
}

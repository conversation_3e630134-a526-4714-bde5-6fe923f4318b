{"doc": " 应用用户管理Service业务层处理\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询应用用户\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.app.domain.bo.AppUserManageBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询应用用户列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.app.domain.bo.AppUserManageBo"], "doc": " 查询应用用户列表\n"}, {"name": "exportUserList", "paramTypes": ["org.dromara.app.domain.bo.AppUserManageBo"], "doc": " 导出应用用户列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.app.domain.bo.AppUserManageBo"], "doc": " 新增应用用户\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.app.domain.bo.AppUserManageBo"], "doc": " 修改应用用户\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.app.domain.AppUser"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 批量删除应用用户\n"}, {"name": "checkEmailUnique", "paramTypes": ["org.dromara.app.domain.bo.AppUserManageBo"], "doc": " 校验邮箱是否唯一\n"}, {"name": "checkPhoneUnique", "paramTypes": ["org.dromara.app.domain.bo.AppUserManageBo"], "doc": " 校验手机号是否唯一\n"}, {"name": "checkStudentIdUnique", "paramTypes": ["org.dromara.app.domain.bo.AppUserManageBo"], "doc": " 校验学号是否唯一\n"}, {"name": "resetUserPassword", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 重置用户密码\n"}, {"name": "changeUserStatus", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 修改用户状态\n"}], "constructors": []}
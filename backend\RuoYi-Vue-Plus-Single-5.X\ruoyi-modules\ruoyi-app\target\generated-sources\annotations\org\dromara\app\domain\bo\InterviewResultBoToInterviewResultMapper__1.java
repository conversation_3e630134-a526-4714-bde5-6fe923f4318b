package org.dromara.app.domain.bo;

import io.github.linpeilie.AutoMapperConfig__158;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.InterviewResult;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__158.class,
    uses = {},
    imports = {}
)
public interface InterviewResultBoToInterviewResultMapper__1 extends BaseMapper<InterviewResultBo, InterviewResult> {
}

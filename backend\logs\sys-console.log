2025-08-02 10:58:07 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-02 10:58:07 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.10 with PID 20620 (C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code\backend)
2025-08-02 10:58:07 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-02 10:58:16 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.CannotLoadBeanClassException: Error loading class [org.dromara.web.service.impl.XcxAuthStrategy] for bean with name 'xcxAuthStrategy' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\service\impl\XcxAuthStrategy.class]: problem with class file or dependent class
2025-08-02 10:58:16 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.CannotLoadBeanClassException: Error loading class [org.dromara.web.service.impl.XcxAuthStrategy] for bean with name 'xcxAuthStrategy' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\service\impl\XcxAuthStrategy.class]: problem with class file or dependent class
	at org.springframework.beans.factory.support.AbstractBeanFactory.resolveBeanClass(AbstractBeanFactory.java:1588)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.determineTargetType(AbstractAutowireCapableBeanFactory.java:692)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.predictBeanType(AbstractAutowireCapableBeanFactory.java:660)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getType(AbstractBeanFactory.java:738)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAnnotationOnBean(DefaultListableBeanFactory.java:817)
	at org.springframework.boot.sql.init.dependency.AnnotationDependsOnDatabaseInitializationDetector.detect(AnnotationDependsOnDatabaseInitializationDetector.java:36)
	at org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer$DependsOnDatabaseInitializationPostProcessor.detectDependsOnInitializationBeanNames(DatabaseInitializationDependencyConfigurer.java:152)
	at org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer$DependsOnDatabaseInitializationPostProcessor.postProcessBeanFactory(DatabaseInitializationDependencyConfigurer.java:115)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:363)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:197)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:791)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:609)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.dromara.DromaraApplication.main(DromaraApplication.java:25)
Caused by: java.lang.ClassFormatError: Duplicate method name "login" with signature "(Ljava.lang.String;LSysClientVo;)Lorg.dromara.web.domain.vo.LoginVo;" in class file org/dromara/web/service/impl/XcxAuthStrategy
	at java.base/java.lang.ClassLoader.defineClass1(Native Method)
	at java.base/java.lang.ClassLoader.defineClass(ClassLoader.java:1012)
	at java.base/java.security.SecureClassLoader.defineClass(SecureClassLoader.java:150)
	at java.base/jdk.internal.loader.BuiltinClassLoader.defineClass(BuiltinClassLoader.java:862)
	at java.base/jdk.internal.loader.BuiltinClassLoader.findClassOnClassPathOrNull(BuiltinClassLoader.java:760)
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClassOrNull(BuiltinClassLoader.java:681)
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:639)
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:520)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:467)
	at org.springframework.util.ClassUtils.forName(ClassUtils.java:321)
	at org.springframework.beans.factory.support.AbstractBeanDefinition.resolveBeanClass(AbstractBeanDefinition.java:503)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doResolveBeanClass(AbstractBeanFactory.java:1653)
	at org.springframework.beans.factory.support.AbstractBeanFactory.resolveBeanClass(AbstractBeanFactory.java:1578)
	... 16 common frames omitted
2025-08-02 11:20:15 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-02 11:20:15 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.10 with PID 28068 (C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code\backend)
2025-08-02 11:20:15 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-02 11:20:28 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-08-02 11:20:28 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-02 11:20:28 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-02 11:20:28 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-02 11:20:46 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@75a5a912
2025-08-02 11:20:46 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-02 11:20:46 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-02 11:20:46 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-02 11:20:48 [main] WARN  c.b.m.c.injector.methods.SelectByIds - [org.dromara.app.mapper.BadgeMapper.selectByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectByIds]
2025-08-02 11:20:48 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.searchMessages] is ignored, because it exists, maybe from xml file
2025-08-02 11:20:48 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.getMessageStats] is ignored, because it exists, maybe from xml file
2025-08-02 11:20:48 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.countSessionMessages] is ignored, because it exists, maybe from xml file
2025-08-02 11:20:48 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.selectRecentMessages] is ignored, because it exists, maybe from xml file
2025-08-02 11:20:48 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.countUserSessions] is ignored, because it exists, maybe from xml file
2025-08-02 11:20:48 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectSessionStats] is ignored, because it exists, maybe from xml file
2025-08-02 11:20:48 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectActiveSessions] is ignored, because it exists, maybe from xml file
2025-08-02 11:20:48 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectUserSessionsByAgent] is ignored, because it exists, maybe from xml file
2025-08-02 11:20:48 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.getPopularAgentTypes] is ignored, because it exists, maybe from xml file
2025-08-02 11:20:48 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectRecentSessions] is ignored, because it exists, maybe from xml file
2025-08-02 11:20:48 [main] WARN  c.b.m.c.injector.methods.SelectByIds - [org.dromara.app.mapper.InterviewQuestionMapper.selectByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectByIds]
2025-08-02 11:20:48 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ToolCallMapper.getUserToolStats] is ignored, because it exists, maybe from xml file
2025-08-02 11:20:50 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\controller\AuthController.class]: Unsatisfied dependency expressed through constructor parameter 6: No qualifying bean of type 'java.util.concurrent.ScheduledExecutorService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-08-02 11:20:50 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-02 11:20:50 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-08-02 11:21:03 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-08-02 11:21:03 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-02 11:21:03 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-02 11:21:03 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\controller\AuthController.class]: Unsatisfied dependency expressed through constructor parameter 6: No qualifying bean of type 'java.util.concurrent.ScheduledExecutorService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.dromara.DromaraApplication.main(DromaraApplication.java:25)
Caused by: org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'java.util.concurrent.ScheduledExecutorService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.raiseNoMatchingBeanFound(DefaultListableBeanFactory.java:2280)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1703)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 19 common frames omitted
2025-08-02 11:30:13 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-02 11:30:13 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.10 with PID 5948 (C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code\backend)
2025-08-02 11:30:13 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-02 11:30:21 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-08-02 11:30:21 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-02 11:30:21 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-02 11:30:21 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-02 11:30:39 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@5fc42998
2025-08-02 11:30:39 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-02 11:30:39 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-02 11:30:39 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-02 11:30:41 [main] WARN  c.b.m.c.injector.methods.SelectByIds - [org.dromara.app.mapper.BadgeMapper.selectByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectByIds]
2025-08-02 11:30:41 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.searchMessages] is ignored, because it exists, maybe from xml file
2025-08-02 11:30:41 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.getMessageStats] is ignored, because it exists, maybe from xml file
2025-08-02 11:30:41 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.countSessionMessages] is ignored, because it exists, maybe from xml file
2025-08-02 11:30:41 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.selectRecentMessages] is ignored, because it exists, maybe from xml file
2025-08-02 11:30:41 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.countUserSessions] is ignored, because it exists, maybe from xml file
2025-08-02 11:30:41 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectSessionStats] is ignored, because it exists, maybe from xml file
2025-08-02 11:30:41 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.getPopularAgentTypes] is ignored, because it exists, maybe from xml file
2025-08-02 11:30:41 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectUserSessionsByAgent] is ignored, because it exists, maybe from xml file
2025-08-02 11:30:41 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectActiveSessions] is ignored, because it exists, maybe from xml file
2025-08-02 11:30:41 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectRecentSessions] is ignored, because it exists, maybe from xml file
2025-08-02 11:30:41 [main] WARN  c.b.m.c.injector.methods.SelectByIds - [org.dromara.app.mapper.InterviewQuestionMapper.selectByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectByIds]
2025-08-02 11:30:41 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ToolCallMapper.getUserToolStats] is ignored, because it exists, maybe from xml file
2025-08-02 11:30:42 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\controller\AuthController.class]: Unsatisfied dependency expressed through constructor parameter 6: No qualifying bean of type 'java.util.concurrent.ScheduledExecutorService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-08-02 11:30:42 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-02 11:30:42 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-08-02 11:30:57 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-08-02 11:30:57 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-02 11:30:57 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-02 11:30:57 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\controller\AuthController.class]: Unsatisfied dependency expressed through constructor parameter 6: No qualifying bean of type 'java.util.concurrent.ScheduledExecutorService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.dromara.DromaraApplication.main(DromaraApplication.java:25)
Caused by: org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'java.util.concurrent.ScheduledExecutorService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.raiseNoMatchingBeanFound(DefaultListableBeanFactory.java:2280)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1703)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 19 common frames omitted
2025-08-02 11:37:14 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-02 11:37:14 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.10 with PID 29844 (C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code\backend)
2025-08-02 11:37:14 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-02 11:37:25 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-08-02 11:37:25 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-02 11:37:25 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-02 11:37:25 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-02 11:37:45 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@11d557a5
2025-08-02 11:37:45 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-02 11:37:45 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-02 11:37:45 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-02 11:37:47 [main] WARN  c.b.m.c.injector.methods.SelectByIds - [org.dromara.app.mapper.BadgeMapper.selectByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectByIds]
2025-08-02 11:37:47 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.searchMessages] is ignored, because it exists, maybe from xml file
2025-08-02 11:37:47 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.getMessageStats] is ignored, because it exists, maybe from xml file
2025-08-02 11:37:47 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.countSessionMessages] is ignored, because it exists, maybe from xml file
2025-08-02 11:37:47 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.selectRecentMessages] is ignored, because it exists, maybe from xml file
2025-08-02 11:37:47 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.countUserSessions] is ignored, because it exists, maybe from xml file
2025-08-02 11:37:47 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectUserSessionsByAgent] is ignored, because it exists, maybe from xml file
2025-08-02 11:37:47 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.getPopularAgentTypes] is ignored, because it exists, maybe from xml file
2025-08-02 11:37:47 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectRecentSessions] is ignored, because it exists, maybe from xml file
2025-08-02 11:37:47 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectActiveSessions] is ignored, because it exists, maybe from xml file
2025-08-02 11:37:47 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectSessionStats] is ignored, because it exists, maybe from xml file
2025-08-02 11:37:47 [main] WARN  c.b.m.c.injector.methods.SelectByIds - [org.dromara.app.mapper.InterviewQuestionMapper.selectByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectByIds]
2025-08-02 11:37:47 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ToolCallMapper.getUserToolStats] is ignored, because it exists, maybe from xml file
2025-08-02 11:37:50 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'captchaController' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\controller\CaptchaController.class]: Unsatisfied dependency expressed through constructor parameter 1: No qualifying bean of type 'org.dromara.common.mail.config.properties.MailProperties' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-08-02 11:37:50 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-02 11:37:50 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-08-02 11:38:05 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-08-02 11:38:05 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-02 11:38:05 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-02 11:38:06 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'captchaController' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\controller\CaptchaController.class]: Unsatisfied dependency expressed through constructor parameter 1: No qualifying bean of type 'org.dromara.common.mail.config.properties.MailProperties' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.dromara.DromaraApplication.main(DromaraApplication.java:25)
Caused by: org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'org.dromara.common.mail.config.properties.MailProperties' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.raiseNoMatchingBeanFound(DefaultListableBeanFactory.java:2280)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1703)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 19 common frames omitted
2025-08-02 11:48:17 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-02 11:48:17 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.10 with PID 11168 (C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code\backend)
2025-08-02 11:48:17 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-02 11:48:25 [main] WARN  io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-08-02 11:48:25 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-02 11:48:26 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-02 11:48:26 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-02 11:48:44 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@5fc42998
2025-08-02 11:48:44 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-02 11:48:44 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-02 11:48:44 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-02 11:48:45 [main] WARN  c.b.m.c.injector.methods.SelectByIds - [org.dromara.app.mapper.BadgeMapper.selectByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectByIds]
2025-08-02 11:48:45 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.searchMessages] is ignored, because it exists, maybe from xml file
2025-08-02 11:48:45 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.getMessageStats] is ignored, because it exists, maybe from xml file
2025-08-02 11:48:45 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.countSessionMessages] is ignored, because it exists, maybe from xml file
2025-08-02 11:48:45 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.selectRecentMessages] is ignored, because it exists, maybe from xml file
2025-08-02 11:48:45 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.countUserSessions] is ignored, because it exists, maybe from xml file
2025-08-02 11:48:45 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectSessionStats] is ignored, because it exists, maybe from xml file
2025-08-02 11:48:45 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.getPopularAgentTypes] is ignored, because it exists, maybe from xml file
2025-08-02 11:48:45 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectActiveSessions] is ignored, because it exists, maybe from xml file
2025-08-02 11:48:45 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectUserSessionsByAgent] is ignored, because it exists, maybe from xml file
2025-08-02 11:48:45 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectRecentSessions] is ignored, because it exists, maybe from xml file
2025-08-02 11:48:45 [main] WARN  c.b.m.c.injector.methods.SelectByIds - [org.dromara.app.mapper.InterviewQuestionMapper.selectByIds] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectByIds]
2025-08-02 11:48:45 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ToolCallMapper.getUserToolStats] is ignored, because it exists, maybe from xml file
2025-08-02 11:48:47 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'captchaController' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\controller\CaptchaController.class]: Unsatisfied dependency expressed through constructor parameter 1: No qualifying bean of type 'org.dromara.common.mail.config.properties.MailProperties' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-08-02 11:48:47 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-02 11:48:47 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-08-02 11:49:01 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-08-02 11:49:01 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-02 11:49:01 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-08-02 11:49:01 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'captchaController' defined in file [C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes\org\dromara\web\controller\CaptchaController.class]: Unsatisfied dependency expressed through constructor parameter 1: No qualifying bean of type 'org.dromara.common.mail.config.properties.MailProperties' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.dromara.DromaraApplication.main(DromaraApplication.java:25)
Caused by: org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'org.dromara.common.mail.config.properties.MailProperties' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.raiseNoMatchingBeanFound(DefaultListableBeanFactory.java:2280)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1703)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 19 common frames omitted

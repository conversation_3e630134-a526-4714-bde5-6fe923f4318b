package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__158;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.vo.VideoDetailVo;
import org.dromara.app.domain.vo.VideoDetailVoToVideoMapper__1;
import org.dromara.app.utils.VideoMappingUtils;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__158.class,
    uses = {VideoMappingUtils.class,VideoDetailVoToVideoMapper__1.class},
    imports = {}
)
public interface VideoToVideoDetailVoMapper__1 extends BaseMapper<Video, VideoDetailVo> {
}

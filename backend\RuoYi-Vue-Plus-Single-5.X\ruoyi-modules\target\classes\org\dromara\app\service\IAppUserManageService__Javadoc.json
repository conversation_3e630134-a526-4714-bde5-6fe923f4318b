{"doc": " 应用用户管理Service接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询应用用户\n\n @param userId 用户主键\n @return 应用用户\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.app.domain.bo.AppUserManageBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询应用用户列表\n\n @param bo        查询条件\n @param pageQuery 分页查询条件\n @return 应用用户分页列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.app.domain.bo.AppUserManageBo"], "doc": " 查询应用用户列表\n\n @param bo 查询条件\n @return 应用用户列表\n"}, {"name": "exportUserList", "paramTypes": ["org.dromara.app.domain.bo.AppUserManageBo"], "doc": " 导出应用用户列表\n\n @param bo 查询条件\n @return 应用用户列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.app.domain.bo.AppUserManageBo"], "doc": " 新增应用用户\n\n @param bo 应用用户信息\n @return 新增结果\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.app.domain.bo.AppUserManageBo"], "doc": " 修改应用用户\n\n @param bo 应用用户信息\n @return 修改结果\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除应用用户信息\n\n @param ids     待删除的主键集合\n @param isValid 是否校验,true-删除前校验,false-不校验\n @return 删除结果\n"}, {"name": "checkEmailUnique", "paramTypes": ["org.dromara.app.domain.bo.AppUserManageBo"], "doc": " 校验用户名称是否唯一\n\n @param user 用户信息\n @return 结果\n"}, {"name": "checkPhoneUnique", "paramTypes": ["org.dromara.app.domain.bo.AppUserManageBo"], "doc": " 校验手机号码是否唯一\n\n @param user 用户信息\n @return 结果\n"}, {"name": "checkStudentIdUnique", "paramTypes": ["org.dromara.app.domain.bo.AppUserManageBo"], "doc": " 校验学号是否唯一\n\n @param user 用户信息\n @return 结果\n"}, {"name": "resetUserPassword", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 重置用户密码\n\n @param userId      用户ID\n @param newPassword 新密码\n @return 重置结果\n"}, {"name": "changeUserStatus", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 修改用户状态\n\n @param userId 用户ID\n @param status 状态\n @return 修改结果\n"}], "constructors": []}
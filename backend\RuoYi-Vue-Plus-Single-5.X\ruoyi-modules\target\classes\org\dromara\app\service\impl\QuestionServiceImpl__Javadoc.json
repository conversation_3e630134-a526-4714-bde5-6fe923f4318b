{"doc": " 题目管理Service业务层处理\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "convertQuestionToVo", "paramTypes": ["org.dromara.app.domain.Question"], "doc": " 转换Question到QuestionVo\n"}, {"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询题目\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.app.domain.bo.QuestionBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询题目列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.app.domain.bo.QuestionBo"], "doc": " 查询题目列表\n"}, {"name": "queryByBankId", "paramTypes": ["java.lang.Long", "org.dromara.app.domain.bo.QuestionBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 根据题库ID查询题目列表\n"}, {"name": "buildQueryWrapper", "paramTypes": ["org.dromara.app.domain.bo.QuestionBo"], "doc": " 构建查询条件\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.app.domain.bo.QuestionBo"], "doc": " 新增题目\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.app.domain.bo.QuestionBo"], "doc": " 修改题目\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除题目信息\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.app.domain.Question"], "doc": " 保存前的数据校验\n"}, {"name": "set<PERSON>efault<PERSON><PERSON><PERSON>", "paramTypes": ["org.dromara.app.domain.Question"], "doc": " 设置默认值\n"}, {"name": "existsQuestionCode", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 检查题目编码是否存在\n"}, {"name": "updateBankQuestionCount", "paramTypes": ["java.lang.Long"], "doc": " 更新题库题目数量\n"}, {"name": "importQuestion", "paramTypes": ["java.util.List", "java.lang.Long", "java.lang.Bo<PERSON>an", "java.lang.String"], "doc": " 批量导入题目\n"}, {"name": "exportQuestionList", "paramTypes": ["org.dromara.app.domain.bo.QuestionBo"], "doc": " 导出题目列表\n"}, {"name": "updateStatus", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 更新题目状态\n"}, {"name": "batchUpdateStatus", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 批量更新题目状态\n"}, {"name": "updateDifficulty", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 更新题目难度\n"}, {"name": "batchUpdateDifficulty", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 批量更新题目难度\n"}, {"name": "updatePracticeStatistics", "paramTypes": ["java.lang.Long", "java.lang.Bo<PERSON>an"], "doc": " 更新练习统计\n"}, {"name": "getBankQuestionStatistics", "paramTypes": ["java.lang.Long"], "doc": " 获取题库下的题目统计\n"}, {"name": "aiScoreQuestion", "paramTypes": ["java.lang.Long"], "doc": " 题目AI评分\n"}, {"name": "batchAiScoreQuestions", "paramTypes": ["java.util.List"], "doc": " 批量AI评分\n"}, {"name": "batchSetCategory", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 批量设置题目分类\n"}, {"name": "batchSetDifficulty", "paramTypes": ["java.util.List", "java.lang.Integer"], "doc": " 批量设置题目难度\n"}], "constructors": []}
package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__158;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.KnowledgeDocumentBoToKnowledgeDocumentMapper__1;
import org.dromara.app.domain.vo.KnowledgeDocumentVo;
import org.dromara.app.domain.vo.KnowledgeDocumentVoToKnowledgeDocumentMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__158.class,
    uses = {KnowledgeDocumentBoToKnowledgeDocumentMapper__1.class,KnowledgeDocumentVoToKnowledgeDocumentMapper__1.class},
    imports = {}
)
public interface KnowledgeDocumentToKnowledgeDocumentVoMapper__1 extends BaseMapper<KnowledgeDocument, KnowledgeDocumentVo> {
}

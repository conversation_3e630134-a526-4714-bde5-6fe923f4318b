package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__152;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysPaymentOrder;
import org.dromara.system.domain.SysPaymentOrderToPaymentOrderVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__152.class,
    uses = {SysPaymentOrderToPaymentOrderVoMapper.class},
    imports = {}
)
public interface PaymentOrderVoToSysPaymentOrderMapper extends BaseMapper<PaymentOrderVo, SysPaymentOrder> {
}

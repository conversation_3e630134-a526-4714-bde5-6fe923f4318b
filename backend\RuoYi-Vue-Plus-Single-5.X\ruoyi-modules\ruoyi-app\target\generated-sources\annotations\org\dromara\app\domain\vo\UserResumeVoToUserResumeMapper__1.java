package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__158;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.UserResume;
import org.dromara.app.domain.UserResumeToUserResumeVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__158.class,
    uses = {UserResumeToUserResumeVoMapper__1.class},
    imports = {}
)
public interface UserResumeVoToUserResumeMapper__1 extends BaseMapper<UserResumeVo, UserResume> {
}

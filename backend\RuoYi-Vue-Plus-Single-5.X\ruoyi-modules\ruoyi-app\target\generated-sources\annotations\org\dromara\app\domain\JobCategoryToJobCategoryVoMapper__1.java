package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__158;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.vo.JobCategoryVo;
import org.dromara.app.domain.vo.JobCategoryVoToJobCategoryMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__158.class,
    uses = {JobCategoryVoToJobCategoryMapper__1.class},
    imports = {}
)
public interface JobCategoryToJobCategoryVoMapper__1 extends BaseMapper<JobCategory, JobCategoryVo> {
}

package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__158;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.AppUserManageBoToAppUserMapper__1;
import org.dromara.app.domain.vo.AppUserManageVo;
import org.dromara.app.domain.vo.AppUserManageVoToAppUserMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__158.class,
    uses = {AppUserManageBoToAppUserMapper__1.class,AppUserManageVoToAppUserMapper__1.class},
    imports = {}
)
public interface AppUserToAppUserManageVoMapper__1 extends BaseMapper<AppUser, AppUserManageVo> {
}

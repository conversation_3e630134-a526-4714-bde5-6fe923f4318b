{"doc": " 题目Mapper接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectQuestionsByCategory", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Integer", "java.lang.Integer"], "doc": " 根据题库ID和分类查询题目列表\n\n @param bankId   题库ID\n @param category 分类\n @return 题目列表\n"}, {"name": "selectRecommendedQuestions", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 根据题库ID查询推荐题目\n\n @param bankId 题库ID\n @param limit  数量限制\n @return 推荐题目列表\n"}, {"name": "selectCategoriesByBankId", "paramTypes": ["java.lang.Long"], "doc": " 根据题库ID查询所有分类\n\n @param bankId 题库ID\n @return 分类列表\n"}], "constructors": []}
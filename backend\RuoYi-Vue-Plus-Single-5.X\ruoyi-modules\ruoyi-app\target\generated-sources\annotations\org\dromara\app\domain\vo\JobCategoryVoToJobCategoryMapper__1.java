package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__158;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.JobCategory;
import org.dromara.app.domain.JobCategoryToJobCategoryVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__158.class,
    uses = {JobCategoryToJobCategoryVoMapper__1.class},
    imports = {}
)
public interface JobCategoryVoToJobCategoryMapper__1 extends BaseMapper<JobCategoryVo, JobCategory> {
}

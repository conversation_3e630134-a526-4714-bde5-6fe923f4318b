<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="93e1676b-1766-4496-896a-93c7c0591d99" name="更改" comment="">
      <change afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/MajorSimpleVo.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/PaymentOrderAppVo.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/ruoyi-monitor-admin.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/sys-error.2025-07-24.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/sys-info.2025-07-24.log" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.idea/misc.xml" beforeDir="false" afterPath="$PROJECT_DIR$/../.idea/misc.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-admin/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-admin/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-admin/src/main/java/org/dromara/DromaraApplication.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-admin/src/main/java/org/dromara/DromaraApplication.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-admin/src/main/java/org/dromara/web/controller/AuthController.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-admin/src/main/java/org/dromara/web/controller/AuthController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-admin/src/main/resources/application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-admin/src/main/resources/application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-admin/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-admin/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-common/ruoyi-common-core/src/main/java/org/dromara/common/core/config/ThreadPoolConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-common/ruoyi-common-core/src/main/java/org/dromara/common/core/config/ThreadPoolConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-common/ruoyi-common-excel/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-common/ruoyi-common-excel/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/config/OllamaConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/config/OllamaConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/controller/interview/ReportController.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/controller/interview/ReportController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/controller/learning/LearningController.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/controller/learning/LearningController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/controller/learning/QuestionCommentController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/controller/pay/PayController.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/controller/pay/PayController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/InterviewReport.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/InterviewReport.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/LearningProgress.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/LearningProgress.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/LearningResource.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/LearningResource.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/bo/QuestionCommentBo.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/bo/QuestionCommentBo.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/InterviewResultResponseVo.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/InterviewResultResponseVo.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/MajorVo.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/PaymentOrderVo.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/QuestionBankVo.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/QuestionBankVo.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/QuestionVO.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/domain/vo/QuestionVO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/mapper/QuestionBankBookmarkMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/mapper/QuestionBankBookmarkMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/mapper/QuestionBankMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/mapper/QuestionBankMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/mapper/QuestionCommentMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/mapper/QuestionCommentMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/ILearningService.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/ILearningService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/IPaymentService.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/IPaymentService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/AchievementManageServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/AchievementManageServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/FeedbackServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/FeedbackServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/InterviewAnalysisServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/InterviewAnalysisServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/InterviewServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/InterviewServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/JobServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/JobServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/LearningServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/LearningServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/PaymentServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/PaymentServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/PaymentSseServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/PaymentSseServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/ToolCallServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/ToolCallServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/UserResumeServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/UserResumeServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/XunfeiServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/service/impl/XunfeiServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/websocket/InterviewWebSocketHandler.java" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-app/src/main/java/org/dromara/app/websocket/InterviewWebSocketHandler.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-system/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/RuoYi-Vue-Plus-Single-5.X/ruoyi-modules/ruoyi-system/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/.eslintrc-auto-import.json" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/.eslintrc-auto-import.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/manifest.json" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/manifest.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/pages.json" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/pages.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/types/auto-import.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/types/auto-import.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/src/types/uni-pages.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/src/types/uni-pages.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../front/unibest-main/vite.config.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../front/unibest-main/vite.config.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../target/classes/META-INF/mps/autoMapper" beforeDir="false" afterPath="$PROJECT_DIR$/../target/classes/META-INF/mps/autoMapper" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../target/classes/META-INF/mps/mappers" beforeDir="false" afterPath="$PROJECT_DIR$/../target/classes/META-INF/mps/mappers" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2zwn7wg0gjXD3tYoaIywbdLj9Xj" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.ruoyi-vue-plus [compile].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.DromaraApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.MonitorAdminApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/softwart-xunfei-code&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;全局库&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.DromaraApplication">
    <configuration name="DromaraApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-admin" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.DromaraApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="MonitorAdminApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-monitor-admin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.monitor.admin.MonitorAdminApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RuoyiMcpServeApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-mcp-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.mcpserve.RuoyiMcpServeApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SnailJobServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="ruoyi-snailjob-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.dromara.snailjob.SnailJobServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="93e1676b-1766-4496-896a-93c7c0591d99" name="更改" comment="" />
      <created>1752650839762</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752650839762</updated>
      <workItem from="1752650840855" duration="2405000" />
      <workItem from="1753356844139" duration="12000" />
      <workItem from="1754014926812" duration="3658000" />
      <workItem from="1754022718941" duration="1994000" />
      <workItem from="1754028728263" duration="7506000" />
      <workItem from="1754052297985" duration="2370000" />
      <workItem from="1754098157655" duration="3437000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>
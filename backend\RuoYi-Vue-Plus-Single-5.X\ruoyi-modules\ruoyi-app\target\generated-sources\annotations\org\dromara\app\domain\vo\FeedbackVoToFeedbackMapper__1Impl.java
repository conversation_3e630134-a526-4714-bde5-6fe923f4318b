package org.dromara.app.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.app.domain.Feedback;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-02T11:12:25+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class FeedbackVoToFeedbackMapper__1Impl implements FeedbackVoToFeedbackMapper__1 {

    @Override
    public Feedback convert(FeedbackVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Feedback feedback = new Feedback();

        feedback.setCreateTime( arg0.getCreateTime() );
        feedback.setUpdateTime( arg0.getUpdateTime() );
        feedback.setAppVersion( arg0.getAppVersion() );
        feedback.setContactInfo( arg0.getContactInfo() );
        feedback.setContent( arg0.getContent() );
        feedback.setDeviceInfo( arg0.getDeviceInfo() );
        feedback.setHandler( arg0.getHandler() );
        feedback.setId( arg0.getId() );
        feedback.setPlatform( arg0.getPlatform() );
        feedback.setReply( arg0.getReply() );
        feedback.setStatus( arg0.getStatus() );
        feedback.setType( arg0.getType() );
        feedback.setUserId( arg0.getUserId() );

        return feedback;
    }

    @Override
    public Feedback convert(FeedbackVo arg0, Feedback arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setAppVersion( arg0.getAppVersion() );
        arg1.setContactInfo( arg0.getContactInfo() );
        arg1.setContent( arg0.getContent() );
        arg1.setDeviceInfo( arg0.getDeviceInfo() );
        arg1.setHandler( arg0.getHandler() );
        arg1.setId( arg0.getId() );
        arg1.setPlatform( arg0.getPlatform() );
        arg1.setReply( arg0.getReply() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setType( arg0.getType() );
        arg1.setUserId( arg0.getUserId() );

        return arg1;
    }
}

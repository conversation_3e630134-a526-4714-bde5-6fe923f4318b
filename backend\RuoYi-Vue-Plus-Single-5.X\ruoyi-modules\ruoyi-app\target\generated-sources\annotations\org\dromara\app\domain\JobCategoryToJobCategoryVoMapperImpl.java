package org.dromara.app.domain;

import java.time.LocalDateTime;
import java.time.ZoneId;
import javax.annotation.processing.Generated;
import org.dromara.app.domain.vo.JobCategoryVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-02T10:57:36+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.10 (Oracle Corporation)"
)
@Component
public class JobCategoryToJobCategoryVoMapperImpl implements JobCategoryToJobCategoryVoMapper {

    @Override
    public JobCategoryVo convert(JobCategory arg0) {
        if ( arg0 == null ) {
            return null;
        }

        JobCategoryVo jobCategoryVo = new JobCategoryVo();

        jobCategoryVo.setId( arg0.getId() );
        jobCategoryVo.setName( arg0.getName() );
        jobCategoryVo.setIcon( arg0.getIcon() );
        jobCategoryVo.setColor( arg0.getColor() );
        jobCategoryVo.setDescription( arg0.getDescription() );
        jobCategoryVo.setSortOrder( arg0.getSortOrder() );
        jobCategoryVo.setStatus( arg0.getStatus() );
        if ( arg0.getCreateTime() != null ) {
            jobCategoryVo.setCreateTime( LocalDateTime.ofInstant( arg0.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        if ( arg0.getUpdateTime() != null ) {
            jobCategoryVo.setUpdateTime( LocalDateTime.ofInstant( arg0.getUpdateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }

        return jobCategoryVo;
    }

    @Override
    public JobCategoryVo convert(JobCategory arg0, JobCategoryVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setIcon( arg0.getIcon() );
        arg1.setColor( arg0.getColor() );
        arg1.setDescription( arg0.getDescription() );
        arg1.setSortOrder( arg0.getSortOrder() );
        arg1.setStatus( arg0.getStatus() );
        if ( arg0.getCreateTime() != null ) {
            arg1.setCreateTime( LocalDateTime.ofInstant( arg0.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        else {
            arg1.setCreateTime( null );
        }
        if ( arg0.getUpdateTime() != null ) {
            arg1.setUpdateTime( LocalDateTime.ofInstant( arg0.getUpdateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        else {
            arg1.setUpdateTime( null );
        }

        return arg1;
    }
}

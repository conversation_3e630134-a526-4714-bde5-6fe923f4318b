package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__158;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysPaymentOrder;
import org.dromara.system.domain.SysPaymentOrderToPaymentOrderVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__158.class,
    uses = {SysPaymentOrderToPaymentOrderVoMapper__1.class},
    imports = {}
)
public interface PaymentOrderVoToSysPaymentOrderMapper__1 extends BaseMapper<PaymentOrderVo, SysPaymentOrder> {
}

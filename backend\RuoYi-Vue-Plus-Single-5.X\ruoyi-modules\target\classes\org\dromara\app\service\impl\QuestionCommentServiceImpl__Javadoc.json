{"doc": " 题目评论Service业务层处理\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询题目评论\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.app.domain.bo.QuestionCommentBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询题目评论列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.app.domain.bo.QuestionCommentBo"], "doc": " 查询题目评论列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.app.domain.bo.QuestionCommentBo"], "doc": " 新增题目评论\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.app.domain.bo.QuestionCommentBo"], "doc": " 修改题目评论\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.app.domain.QuestionComment"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection"], "doc": " 批量删除题目评论\n"}, {"name": "getQuestionComments", "paramTypes": ["java.lang.String", "java.lang.Integer", "java.lang.Integer", "java.lang.String", "java.lang.String"], "doc": " 获取题目评论列表（包含回复）\n"}, {"name": "createQuestionComment", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String", "java.lang.Long"], "doc": " 创建题目评论\n"}, {"name": "deleteQuestionComment", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 删除题目评论\n"}, {"name": "likeQuestionComment", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 点赞/取消点赞评论\n"}, {"name": "getRepliesByParentId", "paramTypes": ["java.lang.Long"], "doc": " 获取评论的回复列表\n"}, {"name": "checkUserLikeStatus", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 检查用户是否已点赞评论\n"}, {"name": "batchDeleteComments", "paramTypes": ["java.util.List", "java.lang.Long"], "doc": " 批量删除评论（管理员功能）\n"}, {"name": "auditComment", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Long", "java.lang.String"], "doc": " 审核评论（管理员功能）\n"}, {"name": "toggleCommentTop", "paramTypes": ["java.lang.Long", "java.lang.Bo<PERSON>an", "java.lang.Long"], "doc": " 置顶/取消置顶评论\n"}, {"name": "getCommentStatistics", "paramTypes": ["java.lang.String"], "doc": " 获取评论统计信息\n"}, {"name": "searchComments", "paramTypes": ["java.lang.String", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 搜索评论\n"}, {"name": "reportComment", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 举报评论\n"}, {"name": "exportCommentList", "paramTypes": ["org.dromara.app.domain.bo.QuestionCommentBo"], "doc": " 导出评论列表\n"}], "constructors": []}
package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__152;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.PaymentOrderBoToSysPaymentOrderMapper;
import org.dromara.system.domain.vo.PaymentOrderVo;
import org.dromara.system.domain.vo.PaymentOrderVoToSysPaymentOrderMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__152.class,
    uses = {PaymentOrderBoToSysPaymentOrderMapper.class,PaymentOrderVoToSysPaymentOrderMapper.class},
    imports = {}
)
public interface SysPaymentOrderToPaymentOrderVoMapper extends BaseMapper<SysPaymentOrder, PaymentOrderVo> {
}

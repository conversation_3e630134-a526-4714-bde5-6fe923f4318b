package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__152;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.KnowledgeBase;
import org.dromara.app.domain.KnowledgeBaseToKnowledgeBaseVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__152.class,
    uses = {KnowledgeBaseToKnowledgeBaseVoMapper.class},
    imports = {}
)
public interface KnowledgeBaseVoToKnowledgeBaseMapper extends BaseMapper<KnowledgeBaseVo, KnowledgeBase> {
}

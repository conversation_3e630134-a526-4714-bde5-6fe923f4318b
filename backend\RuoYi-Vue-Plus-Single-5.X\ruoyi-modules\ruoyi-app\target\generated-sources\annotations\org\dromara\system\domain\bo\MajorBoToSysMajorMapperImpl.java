package org.dromara.system.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.system.domain.SysMajor;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-02T10:57:38+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.10 (Oracle Corporation)"
)
@Component
public class MajorBoToSysMajorMapperImpl implements MajorBoToSysMajorMapper {

    @Override
    public SysMajor convert(MajorBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysMajor sysMajor = new SysMajor();

        sysMajor.setSearchValue( arg0.getSearchValue() );
        sysMajor.setCreateDept( arg0.getCreateDept() );
        sysMajor.setCreateBy( arg0.getCreateBy() );
        sysMajor.setCreateTime( arg0.getCreateTime() );
        sysMajor.setUpdateBy( arg0.getUpdateBy() );
        sysMajor.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysMajor.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysMajor.setMajorId( arg0.getMajorId() );
        sysMajor.setMajorCode( arg0.getMajorCode() );
        sysMajor.setMajorName( arg0.getMajorName() );
        sysMajor.setIcon( arg0.getIcon() );
        sysMajor.setColor( arg0.getColor() );
        sysMajor.setQuestionBankCount( arg0.getQuestionBankCount() );
        sysMajor.setSort( arg0.getSort() );
        sysMajor.setStatus( arg0.getStatus() );
        sysMajor.setRemark( arg0.getRemark() );

        return sysMajor;
    }

    @Override
    public SysMajor convert(MajorBo arg0, SysMajor arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setMajorId( arg0.getMajorId() );
        arg1.setMajorCode( arg0.getMajorCode() );
        arg1.setMajorName( arg0.getMajorName() );
        arg1.setIcon( arg0.getIcon() );
        arg1.setColor( arg0.getColor() );
        arg1.setQuestionBankCount( arg0.getQuestionBankCount() );
        arg1.setSort( arg0.getSort() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setRemark( arg0.getRemark() );

        return arg1;
    }
}

package org.dromara.app.domain;

import javax.annotation.processing.Generated;
import org.dromara.app.domain.vo.FeedbackVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-02T10:57:36+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.10 (Oracle Corporation)"
)
@Component
public class FeedbackToFeedbackVoMapperImpl implements FeedbackToFeedbackVoMapper {

    @Override
    public FeedbackVo convert(Feedback arg0) {
        if ( arg0 == null ) {
            return null;
        }

        FeedbackVo feedbackVo = new FeedbackVo();

        feedbackVo.setId( arg0.getId() );
        feedbackVo.setUserId( arg0.getUserId() );
        feedbackVo.setType( arg0.getType() );
        feedbackVo.setContent( arg0.getContent() );
        feedbackVo.setContactInfo( arg0.getContactInfo() );
        feedbackVo.setDeviceInfo( arg0.getDeviceInfo() );
        feedbackVo.setAppVersion( arg0.getAppVersion() );
        feedbackVo.setPlatform( arg0.getPlatform() );
        feedbackVo.setStatus( arg0.getStatus() );
        feedbackVo.setReply( arg0.getReply() );
        feedbackVo.setHandler( arg0.getHandler() );
        feedbackVo.setCreateTime( arg0.getCreateTime() );
        feedbackVo.setUpdateTime( arg0.getUpdateTime() );

        return feedbackVo;
    }

    @Override
    public FeedbackVo convert(Feedback arg0, FeedbackVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setType( arg0.getType() );
        arg1.setContent( arg0.getContent() );
        arg1.setContactInfo( arg0.getContactInfo() );
        arg1.setDeviceInfo( arg0.getDeviceInfo() );
        arg1.setAppVersion( arg0.getAppVersion() );
        arg1.setPlatform( arg0.getPlatform() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setReply( arg0.getReply() );
        arg1.setHandler( arg0.getHandler() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateTime( arg0.getUpdateTime() );

        return arg1;
    }
}

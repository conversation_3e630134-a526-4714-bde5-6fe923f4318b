{"doc": " 应用用户管理视图对象 app_user\n\n <AUTHOR>\n", "fields": [{"name": "userId", "doc": " 用户ID\n"}, {"name": "phone", "doc": " 用户手机号\n"}, {"name": "email", "doc": " 用户邮箱\n"}, {"name": "realName", "doc": " 用户姓名\n"}, {"name": "gender", "doc": " 用户性别（男/女）\n"}, {"name": "studentId", "doc": " 学生学号\n"}, {"name": "major", "doc": " 专业\n"}, {"name": "grade", "doc": " 年级\n"}, {"name": "school", "doc": " 学校名称\n"}, {"name": "introduction", "doc": " 个人简介\n"}, {"name": "avatar", "doc": " 用户头像\n"}, {"name": "status", "doc": " 帐号状态（0正常 1停用）\n"}, {"name": "loginIp", "doc": " 最后登录IP\n"}, {"name": "loginDate", "doc": " 最后登录时间\n"}, {"name": "registeredAt", "doc": " 注册时间\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "updateTime", "doc": " 更新时间\n"}, {"name": "remark", "doc": " 备注\n"}, {"name": "resumeCount", "doc": " 简历数量（非数据库字段）\n"}, {"name": "lastActiveTime", "doc": " 最近活跃时间（非数据库字段）\n"}], "enumConstants": [], "methods": [], "constructors": []}
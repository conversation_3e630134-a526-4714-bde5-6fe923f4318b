{"doc": " 题目评论Service接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询题目评论\n\n @param commentId 评论主键\n @return 题目评论\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.app.domain.bo.QuestionCommentBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询题目评论列表\n\n @param bo        查询条件\n @param pageQuery 分页查询条件\n @return 题目评论分页列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.app.domain.bo.QuestionCommentBo"], "doc": " 查询题目评论列表\n\n @param bo 查询条件\n @return 题目评论列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.app.domain.bo.QuestionCommentBo"], "doc": " 新增题目评论\n\n @param bo 题目评论信息\n @return 新增结果\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.app.domain.bo.QuestionCommentBo"], "doc": " 修改题目评论\n\n @param bo 题目评论信息\n @return 修改结果\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection"], "doc": " 校验并批量删除题目评论信息\n\n @param ids 待删除的主键集合\n @return 删除结果\n"}, {"name": "getQuestionComments", "paramTypes": ["java.lang.String", "java.lang.Integer", "java.lang.Integer", "java.lang.String", "java.lang.String"], "doc": " 获取题目评论列表（包含回复）\n\n @param questionId     题目ID\n @param page           页码\n @param pageSize       每页大小\n @param orderBy        排序字段\n @param orderDirection 排序方向\n @return 评论列表\n"}, {"name": "createQuestionComment", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String", "java.lang.Long"], "doc": " 创建题目评论\n\n @param userId     用户ID\n @param questionId 题目ID\n @param content    评论内容\n @param parentId   父评论ID\n @return 创建的评论\n"}, {"name": "deleteQuestionComment", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 删除题目评论\n\n @param userId    用户ID\n @param commentId 评论ID\n @return 删除结果\n"}, {"name": "likeQuestionComment", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 点赞/取消点赞评论\n\n @param userId    用户ID\n @param commentId 评论ID\n @return 点赞结果\n"}, {"name": "getRepliesByParentId", "paramTypes": ["java.lang.Long"], "doc": " 获取评论的回复列表\n\n @param parentId 父评论ID\n @return 回复列表\n"}, {"name": "checkUserLikeStatus", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 检查用户是否已点赞评论\n\n @param userId    用户ID\n @param commentId 评论ID\n @return 是否已点赞\n"}, {"name": "batchDeleteComments", "paramTypes": ["java.util.List", "java.lang.Long"], "doc": " 批量删除评论（管理员功能）\n\n @param commentIds 评论ID列表\n @param operatorId 操作者ID\n @return 删除结果\n"}, {"name": "auditComment", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Long", "java.lang.String"], "doc": " 审核评论（管理员功能）\n\n @param commentId  评论ID\n @param status     审核状态\n @param operatorId 操作者ID\n @param reason     审核原因\n @return 审核结果\n"}, {"name": "toggleCommentTop", "paramTypes": ["java.lang.Long", "java.lang.Bo<PERSON>an", "java.lang.Long"], "doc": " 置顶/取消置顶评论\n\n @param commentId  评论ID\n @param isTop      是否置顶\n @param operatorId 操作者ID\n @return 操作结果\n"}, {"name": "getCommentStatistics", "paramTypes": ["java.lang.String"], "doc": " 获取评论统计信息\n\n @param questionId 题目ID\n @return 统计信息\n"}, {"name": "searchComments", "paramTypes": ["java.lang.String", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 搜索评论\n\n @param keyword   搜索关键词\n @param pageQuery 分页查询条件\n @return 搜索结果\n"}, {"name": "reportComment", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 举报评论\n\n @param userId      举报用户ID\n @param commentId   评论ID\n @param reason      举报原因\n @param description 详细描述\n @return 举报结果\n"}, {"name": "exportCommentList", "paramTypes": ["org.dromara.app.domain.bo.QuestionCommentBo"], "doc": " 导出评论列表\n\n @param bo 查询条件\n @return 评论列表\n"}], "constructors": []}
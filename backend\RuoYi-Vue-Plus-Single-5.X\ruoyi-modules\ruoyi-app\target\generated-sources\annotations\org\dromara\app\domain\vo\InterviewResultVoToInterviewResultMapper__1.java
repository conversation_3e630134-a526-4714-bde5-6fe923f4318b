package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__158;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.InterviewResult;
import org.dromara.app.domain.InterviewResultToInterviewResultVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__158.class,
    uses = {InterviewResultToInterviewResultVoMapper__1.class},
    imports = {}
)
public interface InterviewResultVoToInterviewResultMapper__1 extends BaseMapper<InterviewResultVo, InterviewResult> {
}

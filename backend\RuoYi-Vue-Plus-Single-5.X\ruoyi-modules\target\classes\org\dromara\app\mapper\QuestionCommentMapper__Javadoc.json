{"doc": " 题目评论Mapper接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectCommentListWithReplies", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "java.lang.Long", "java.lang.String", "java.lang.String"], "doc": " 分页查询题目评论列表（包含用户信息和回复列表）\n\n @param page           分页对象\n @param questionId     题目ID\n @param orderBy        排序字段\n @param orderDirection 排序方向\n @return 评论列表\n"}, {"name": "selectRepliesByParentId", "paramTypes": ["java.lang.Long"], "doc": " 获取评论的回复列表\n\n @param parentId 父评论ID\n @return 回复列表\n"}, {"name": "selectLikeStatus", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 根据用户ID和评论ID查询点赞状态\n\n @param userId    用户ID\n @param commentId 评论ID\n @return 是否已点赞\n"}, {"name": "incrementLikeCount", "paramTypes": ["java.lang.Long"], "doc": " 增加评论点赞数\n\n @param commentId 评论ID\n @return 更新结果\n"}, {"name": "decrementLikeCount", "paramTypes": ["java.lang.Long"], "doc": " 减少评论点赞数\n\n @param commentId 评论ID\n @return 更新结果\n"}, {"name": "incrementReplyCount", "paramTypes": ["java.lang.Long"], "doc": " 增加回复数\n\n @param commentId 评论ID\n @return 更新结果\n"}, {"name": "decrementReplyCount", "paramTypes": ["java.lang.Long"], "doc": " 减少回复数\n\n @param commentId 评论ID\n @return 更新结果\n"}, {"name": "insertLikeRecord", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 插入点赞记录\n\n @param commentId 评论ID\n @param userId    用户ID\n @return 插入结果\n"}, {"name": "deleteLikeRecord", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 删除点赞记录\n\n @param commentId 评论ID\n @param userId    用户ID\n @return 删除结果\n"}, {"name": "updateQuestionCommentCount", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 更新题目评论数\n\n @param questionId 题目ID\n @param increment  增量（正数增加，负数减少）\n @return 更新结果\n"}], "constructors": []}
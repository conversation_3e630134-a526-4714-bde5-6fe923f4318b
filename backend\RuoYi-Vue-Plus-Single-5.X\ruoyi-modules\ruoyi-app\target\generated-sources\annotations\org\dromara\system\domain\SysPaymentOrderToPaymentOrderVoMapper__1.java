package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__158;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.PaymentOrderBoToSysPaymentOrderMapper__1;
import org.dromara.system.domain.vo.PaymentOrderVo;
import org.dromara.system.domain.vo.PaymentOrderVoToSysPaymentOrderMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__158.class,
    uses = {PaymentOrderBoToSysPaymentOrderMapper__1.class,PaymentOrderVoToSysPaymentOrderMapper__1.class},
    imports = {}
)
public interface SysPaymentOrderToPaymentOrderVoMapper__1 extends BaseMapper<SysPaymentOrder, PaymentOrderVo> {
}

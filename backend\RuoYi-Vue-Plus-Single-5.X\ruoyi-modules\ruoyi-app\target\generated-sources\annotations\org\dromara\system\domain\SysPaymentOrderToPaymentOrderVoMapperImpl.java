package org.dromara.system.domain;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.vo.PaymentOrderVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-02T10:57:36+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.10 (Oracle Corporation)"
)
@Component
public class SysPaymentOrderToPaymentOrderVoMapperImpl implements SysPaymentOrderToPaymentOrderVoMapper {

    @Override
    public PaymentOrderVo convert(SysPaymentOrder arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PaymentOrderVo paymentOrderVo = new PaymentOrderVo();

        paymentOrderVo.setOrderId( arg0.getOrderId() );
        paymentOrderVo.setOrderNo( arg0.getOrderNo() );
        paymentOrderVo.setProductId( arg0.getProductId() );
        paymentOrderVo.setProductType( arg0.getProductType() );
        paymentOrderVo.setProductTitle( arg0.getProductTitle() );
        paymentOrderVo.setAmount( arg0.getAmount() );
        paymentOrderVo.setUserId( arg0.getUserId() );
        paymentOrderVo.setPaymentMethod( arg0.getPaymentMethod() );
        paymentOrderVo.setStatus( arg0.getStatus() );
        paymentOrderVo.setAlipayTradeNo( arg0.getAlipayTradeNo() );
        paymentOrderVo.setPayTime( arg0.getPayTime() );
        paymentOrderVo.setExpireTime( arg0.getExpireTime() );
        paymentOrderVo.setClientIp( arg0.getClientIp() );
        paymentOrderVo.setUserAgent( arg0.getUserAgent() );
        paymentOrderVo.setNotifyCount( arg0.getNotifyCount() );
        paymentOrderVo.setLastNotifyTime( arg0.getLastNotifyTime() );
        paymentOrderVo.setNotifyResult( arg0.getNotifyResult() );
        paymentOrderVo.setPayToken( arg0.getPayToken() );
        paymentOrderVo.setPayTokenExpireTime( arg0.getPayTokenExpireTime() );
        paymentOrderVo.setPayTokenUsed( arg0.getPayTokenUsed() );
        paymentOrderVo.setCreateTime( arg0.getCreateTime() );
        paymentOrderVo.setUpdateTime( arg0.getUpdateTime() );
        paymentOrderVo.setRemark( arg0.getRemark() );

        return paymentOrderVo;
    }

    @Override
    public PaymentOrderVo convert(SysPaymentOrder arg0, PaymentOrderVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setOrderId( arg0.getOrderId() );
        arg1.setOrderNo( arg0.getOrderNo() );
        arg1.setProductId( arg0.getProductId() );
        arg1.setProductType( arg0.getProductType() );
        arg1.setProductTitle( arg0.getProductTitle() );
        arg1.setAmount( arg0.getAmount() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setPaymentMethod( arg0.getPaymentMethod() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setAlipayTradeNo( arg0.getAlipayTradeNo() );
        arg1.setPayTime( arg0.getPayTime() );
        arg1.setExpireTime( arg0.getExpireTime() );
        arg1.setClientIp( arg0.getClientIp() );
        arg1.setUserAgent( arg0.getUserAgent() );
        arg1.setNotifyCount( arg0.getNotifyCount() );
        arg1.setLastNotifyTime( arg0.getLastNotifyTime() );
        arg1.setNotifyResult( arg0.getNotifyResult() );
        arg1.setPayToken( arg0.getPayToken() );
        arg1.setPayTokenExpireTime( arg0.getPayTokenExpireTime() );
        arg1.setPayTokenUsed( arg0.getPayTokenUsed() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setRemark( arg0.getRemark() );

        return arg1;
    }
}

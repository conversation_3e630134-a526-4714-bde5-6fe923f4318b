package org.dromara.app.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.app.domain.Feedback;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-02T11:12:24+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class FeedbackBoToFeedbackMapper__1Impl implements FeedbackBoToFeedbackMapper__1 {

    @Override
    public Feedback convert(FeedbackBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Feedback feedback = new Feedback();

        feedback.setCreateBy( arg0.getCreateBy() );
        feedback.setCreateDept( arg0.getCreateDept() );
        feedback.setCreateTime( arg0.getCreateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            feedback.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        feedback.setSearchValue( arg0.getSearchValue() );
        feedback.setUpdateBy( arg0.getUpdateBy() );
        feedback.setUpdateTime( arg0.getUpdateTime() );
        feedback.setAppVersion( arg0.getAppVersion() );
        feedback.setContactInfo( arg0.getContactInfo() );
        feedback.setContent( arg0.getContent() );
        feedback.setDeviceInfo( arg0.getDeviceInfo() );
        feedback.setHandler( arg0.getHandler() );
        feedback.setId( arg0.getId() );
        feedback.setPlatform( arg0.getPlatform() );
        feedback.setReply( arg0.getReply() );
        feedback.setStatus( arg0.getStatus() );
        feedback.setType( arg0.getType() );
        feedback.setUserId( arg0.getUserId() );

        return feedback;
    }

    @Override
    public Feedback convert(FeedbackBo arg0, Feedback arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateTime( arg0.getCreateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setAppVersion( arg0.getAppVersion() );
        arg1.setContactInfo( arg0.getContactInfo() );
        arg1.setContent( arg0.getContent() );
        arg1.setDeviceInfo( arg0.getDeviceInfo() );
        arg1.setHandler( arg0.getHandler() );
        arg1.setId( arg0.getId() );
        arg1.setPlatform( arg0.getPlatform() );
        arg1.setReply( arg0.getReply() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setType( arg0.getType() );
        arg1.setUserId( arg0.getUserId() );

        return arg1;
    }
}

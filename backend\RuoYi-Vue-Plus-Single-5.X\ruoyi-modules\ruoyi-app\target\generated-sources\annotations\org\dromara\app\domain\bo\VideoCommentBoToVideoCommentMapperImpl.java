package org.dromara.app.domain.bo;

import javax.annotation.processing.Generated;
import org.dromara.app.domain.VideoComment;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-02T10:57:37+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.10 (Oracle Corporation)"
)
@Component
public class VideoCommentBoToVideoCommentMapperImpl implements VideoCommentBoToVideoCommentMapper {

    @Override
    public VideoComment convert(VideoCommentBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        VideoComment videoComment = new VideoComment();

        videoComment.setId( arg0.getId() );
        videoComment.setVideoId( arg0.getVideoId() );
        videoComment.setUserId( arg0.getUserId() );
        videoComment.setParentId( arg0.getParentId() );
        videoComment.setContent( arg0.getContent() );

        return videoComment;
    }

    @Override
    public VideoComment convert(VideoCommentBo arg0, VideoComment arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setVideoId( arg0.getVideoId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setParentId( arg0.getParentId() );
        arg1.setContent( arg0.getContent() );

        return arg1;
    }
}

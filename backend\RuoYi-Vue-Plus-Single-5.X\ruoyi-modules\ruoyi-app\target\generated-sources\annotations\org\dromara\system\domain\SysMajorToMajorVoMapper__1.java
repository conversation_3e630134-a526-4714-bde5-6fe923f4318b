package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__158;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.MajorBoToSysMajorMapper__1;
import org.dromara.system.domain.vo.MajorVo;
import org.dromara.system.domain.vo.MajorVoToSysMajorMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__158.class,
    uses = {MajorVoToSysMajorMapper__1.class,MajorBoToSysMajorMapper__1.class},
    imports = {}
)
public interface SysMajorToMajorVoMapper__1 extends BaseMapper<SysMajor, MajorVo> {
}
